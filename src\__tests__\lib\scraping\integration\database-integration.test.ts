/**
 * Database Integration Tests
 * Tests data persistence, schema validation, and transaction handling using MSW
 * @jest-environment node
 */

import { describe, it, expect, beforeEach } from '@jest/globals';
import { ScrapedDataRecord } from '@/lib/scraping/types';
import { mockSupabaseResponse, mockSupabaseData } from '@/__tests__/__mocks__/msw-server';

describe('Database Integration with MSW', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Data Storage', () => {
    it('should store scraped content with all metadata', async () => {
      const scrapedData: ScrapedDataRecord = {
        url: 'https://example.com',
        title: 'Test Page',
        description: 'Test description',
        content: 'Test content with sufficient length for processing',
        scraped_at: new Date().toISOString(),
        metadata: {
          keywords: ['test', 'example'],
          author: 'Test Author',
          publishedDate: '2024-01-01',
          language: 'en',
          canonicalUrl: 'https://example.com',
          images: ['https://example.com/image.jpg'],
          links: ['https://example.com/link1']
        }
      };

      // Mock successful storage response
      mockSupabaseResponse('scraped_content', 'POST', { id: 1, ...scrapedData }, 201);

      // Simulate storing data via Supabase REST API
      const response = await fetch('https://test.supabase.co/rest/v1/scraped_content', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'apikey': 'test-key',
          'Authorization': 'Bearer test-token'
        },
        body: JSON.stringify(scrapedData)
      });
      const result = await response.json();

      expect(response.status).toBe(201);
      expect(result.id).toBe(1);
      expect(result.title).toBe(scrapedData.title);
      expect(result.url).toBe(scrapedData.url);
      expect(result.metadata).toEqual(scrapedData.metadata);
    });

    it('should handle JSONB fields correctly', async () => {
      const complexData = {
        url: 'https://example.com',
        content: 'Test content',
        media_assets: {
          favicon: ['https://example.com/favicon.ico'],
          ogImages: [
            {
              type: 'og:image',
              url: 'https://example.com/image1.jpg',
              priority: 1,
              metadata: {
                width: 1200,
                height: 630,
                format: 'jpeg'
              }
            }
          ]
        },
        additional_pages: [
          {
            success: true,
            content: 'Additional page content',
            url: 'https://example.com/page2',
            metadata: {
              pageType: 'features',
              creditsUsed: 1,
              extractedAt: new Date().toISOString()
            }
          }
        ],
        scraped_at: new Date().toISOString()
      };

      mockSupabaseResponse('scraped_content', 'POST', { id: 2, ...complexData }, 201);

      const response = await fetch('https://test.supabase.co/rest/v1/scraped_content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(complexData)
      });
      const result = await response.json();

      expect(response.status).toBe(201);
      expect(result.media_assets).toEqual(complexData.media_assets);
      expect(result.additional_pages).toEqual(complexData.additional_pages);
    });

    it('should handle database constraints and validation errors', async () => {
      const invalidData = {
        // Missing required url field
        content: 'Test content',
        scraped_at: new Date().toISOString()
      };

      mockSupabaseResponse('scraped_content', 'POST', {
        error: 'null value in column "url" violates not-null constraint',
        code: '23502'
      }, 400);

      const response = await fetch('https://test.supabase.co/rest/v1/scraped_content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidData)
      });
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.error).toBeDefined();
      expect(result.code).toBe('23502');
    });
  });

  describe('Data Retrieval', () => {
    it('should retrieve scraped content by URL', async () => {
      const mockData = {
        id: 1,
        url: 'https://example.com',
        content: 'Retrieved content',
        media_assets: {
          favicon: ['https://example.com/favicon.ico']
        },
        scraped_at: new Date().toISOString()
      };

      mockSupabaseResponse('scraped_content', 'GET', mockData);

      const response = await fetch('https://test.supabase.co/rest/v1/scraped_content?url=eq.https://example.com');
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.url).toBe('https://example.com');
      expect(result.content).toBe('Retrieved content');
    });

    it('should retrieve recent scraped content with pagination', async () => {
      const mockData = [
        {
          id: 1,
          url: 'https://example1.com',
          content: 'Content 1',
          scraped_at: new Date().toISOString()
        },
        {
          id: 2,
          url: 'https://example2.com',
          content: 'Content 2',
          scraped_at: new Date().toISOString()
        }
      ];

      mockSupabaseResponse('scraped_content', 'GET', mockData);

      const response = await fetch('https://test.supabase.co/rest/v1/scraped_content?order=scraped_at.desc&limit=10');
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(2);
    });

    it('should handle missing records gracefully', async () => {
      mockSupabaseResponse('scraped_content', 'GET', {
        error: 'The result contains 0 rows',
        code: 'PGRST116'
      }, 404);

      const response = await fetch('https://test.supabase.co/rest/v1/scraped_content?url=eq.https://nonexistent.com');
      const result = await response.json();

      expect(response.status).toBe(404);
      expect(result.error).toBeDefined();
      expect(result.code).toBe('PGRST116');
    });
  });

  describe('Data Updates', () => {
    it('should update existing scraped content', async () => {
      const updateData = {
        content: 'Updated content',
        updated_at: new Date().toISOString()
      };

      mockSupabaseResponse('scraped_content', 'PATCH', { id: 1, ...updateData });

      const response = await fetch('https://test.supabase.co/rest/v1/scraped_content?id=eq.1', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      });
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.content).toBe('Updated content');
      expect(result.updated_at).toBeDefined();
    });
  });

  describe('Data Deletion', () => {
    it('should delete scraped content by ID', async () => {
      mockSupabaseResponse('scraped_content', 'DELETE', {}, 204);

      const response = await fetch('https://test.supabase.co/rest/v1/scraped_content?id=eq.1', {
        method: 'DELETE'
      });

      expect(response.status).toBe(204);
    });
  });

  describe('Transaction Handling', () => {
    it('should handle bulk operations', async () => {
      const transactionData = [
        {
          url: 'https://example.com',
          content: 'Main content',
          scraped_at: new Date().toISOString()
        },
        {
          url: 'https://example.com/pricing',
          content: 'Pricing content',
          parent_url: 'https://example.com',
          scraped_at: new Date().toISOString()
        }
      ];

      mockSupabaseResponse('scraped_content', 'POST', transactionData, 201);

      const response = await fetch('https://test.supabase.co/rest/v1/scraped_content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(transactionData)
      });
      const result = await response.json();

      expect(response.status).toBe(201);
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(2);
    });
  });
});
